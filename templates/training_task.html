{% extends "base.html" %}

{% block title %}创建训练任务{% endblock %}

{% block nav_training_active %}active{% endblock %}

{% block extra_css %}
<style>
    .training-container {
        max-width: 800px;
        background: var(--card-background);
        border-radius: 12px;
        box-shadow: 0 4px 16px var(--shadow-light);
        padding: 40px;
        margin: 20px auto;
    }

    .form-title {
        color: var(--primary-color);
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--card-background);
        color: var(--text-color);
        box-sizing: border-box;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }

    .repository-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .repository-card {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: var(--card-background);
        text-align: center;
    }

    .repository-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--shadow-medium);
    }

    .repository-card.selected {
        border-color: var(--primary-color);
        background-color: rgba(26, 115, 232, 0.05);
    }

    .repository-card input[type="radio"] {
        display: none;
    }

    .repository-name {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 5px;
    }

    .repository-id {
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    .action-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }

    .action-tabs {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
    }

    .action-tab {
        flex: 1;
        padding: 12px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 600;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        border-bottom: 3px solid transparent;
    }

    .action-tab.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
    }

    .action-tab:hover {
        color: var(--primary-color);
        background-color: rgba(26, 115, 232, 0.05);
    }

    .action-content {
        display: none;
    }

    .action-content.active {
        display: block;
    }

    .tag-list, .branch-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 10px;
        background-color: var(--card-background);
    }

    .tag-item, .branch-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin: 2px 0;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .tag-item:hover, .branch-item:hover {
        background-color: rgba(26, 115, 232, 0.05);
    }

    .tag-item input[type="radio"], .branch-item input[type="radio"] {
        margin-right: 10px;
    }

    .new-tag-input {
        margin-top: 15px;
    }

    .submit-section {
        margin-top: 30px;
        text-align: center;
        padding-top: 20px;
        border-top: 2px solid var(--border-color);
    }

    .submit-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.4);
    }

    .submit-btn:disabled {
        background: var(--text-secondary);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .result-message {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        display: none;
    }

    .result-message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .result-message.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .loading {
        display: none;
        text-align: center;
        margin-top: 20px;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .empty-state {
        text-align: center;
        color: var(--text-secondary);
        font-style: italic;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="training-container">
    <h1 class="form-title">创建训练任务</h1>
    
    <form id="training-form">
        <!-- 任务名称 -->
        <div class="form-group">
            <label for="task_name">任务名称</label>
            <input type="text" id="task_name" name="task_name" class="form-control" 
                   placeholder="请输入训练任务名称" required>
        </div>

        <!-- 仓库选择 -->
        <div class="form-group">
            <label>选择仓库</label>
            <div class="repository-grid">
                {% for repo_id, repo_info in repositories.items() %}
                <div class="repository-card" onclick="selectRepository('{{ repo_id }}')">
                    <input type="radio" name="repository" value="{{ repo_id }}" id="repo_{{ repo_id }}">
                    <div class="repository-name">{{ repo_info.name }}</div>
                    <div class="repository-id">仓库{{ repo_id }}</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 操作选择 -->
        <div class="action-section" id="action-section" style="display: none;">
            <div class="action-tabs">
                <button type="button" class="action-tab active" onclick="switchTab('existing')">
                    选择已有标签
                </button>
                <button type="button" class="action-tab" onclick="switchTab('new')">
                    创建新标签
                </button>
            </div>

            <!-- 选择已有标签 -->
            <div class="action-content active" id="existing-content">
                <div class="form-group">
                    <label>可用标签</label>
                    <div class="tag-list" id="tag-list">
                        <div class="empty-state">请先选择仓库</div>
                    </div>
                </div>
            </div>

            <!-- 创建新标签 -->
            <div class="action-content" id="new-content">
                <div class="form-group">
                    <label>选择分支</label>
                    <div class="branch-list" id="branch-list">
                        <div class="empty-state">请先选择仓库</div>
                    </div>
                </div>
                <div class="form-group new-tag-input">
                    <label for="new_tag_name">新标签名称</label>
                    <input type="text" id="new_tag_name" name="new_tag_name" class="form-control" 
                           placeholder="请输入新标签名称（如：v1.0.0）">
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
            <button type="submit" class="submit-btn" id="submit-btn" disabled>
                创建训练任务
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>正在创建训练任务...</div>
            </div>
            
            <div class="result-message" id="result-message"></div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedRepository = null;
let currentTab = 'existing';

// 选择仓库
function selectRepository(repoId) {
    // 移除之前的选中状态
    document.querySelectorAll('.repository-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // 添加选中状态
    event.currentTarget.classList.add('selected');
    document.getElementById(`repo_${repoId}`).checked = true;
    
    selectedRepository = repoId;
    
    // 显示操作区域
    document.getElementById('action-section').style.display = 'block';
    
    // 加载仓库信息
    loadRepositoryInfo(repoId);
    
    // 检查表单完整性
    checkFormValidity();
}

// 加载仓库信息
function loadRepositoryInfo(repoId) {
    fetch(`/api/repository/${repoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }
            
            // 更新标签列表
            updateTagList(data.tags);
            
            // 更新分支列表
            updateBranchList(data.branches);
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// 更新标签列表
function updateTagList(tags) {
    const tagList = document.getElementById('tag-list');
    
    if (tags.length === 0) {
        tagList.innerHTML = '<div class="empty-state">该仓库暂无可用标签</div>';
        return;
    }
    
    tagList.innerHTML = tags.map(tag => `
        <div class="tag-item">
            <input type="radio" name="selected_tag" value="${tag}" id="tag_${tag}" 
                   onchange="checkFormValidity()">
            <label for="tag_${tag}">${tag}</label>
        </div>
    `).join('');
}

// 更新分支列表
function updateBranchList(branches) {
    const branchList = document.getElementById('branch-list');
    
    if (branches.length === 0) {
        branchList.innerHTML = '<div class="empty-state">该仓库暂无可用分支</div>';
        return;
    }
    
    branchList.innerHTML = branches.map(branch => `
        <div class="branch-item">
            <input type="radio" name="branch_name" value="${branch}" id="branch_${branch}" 
                   onchange="checkFormValidity()">
            <label for="branch_${branch}">${branch}</label>
        </div>
    `).join('');
}

// 切换标签页
function switchTab(tab) {
    // 更新标签页状态
    document.querySelectorAll('.action-tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.action-content').forEach(c => c.classList.remove('active'));
    
    event.target.classList.add('active');
    document.getElementById(`${tab}-content`).classList.add('active');
    
    currentTab = tab;
    
    // 清除之前的选择
    if (tab === 'existing') {
        document.querySelectorAll('input[name="branch_name"]').forEach(input => input.checked = false);
        document.getElementById('new_tag_name').value = '';
    } else {
        document.querySelectorAll('input[name="selected_tag"]').forEach(input => input.checked = false);
    }
    
    checkFormValidity();
}

// 检查表单完整性
function checkFormValidity() {
    const taskName = document.getElementById('task_name').value.trim();
    const repository = selectedRepository;
    let isValid = false;
    
    if (taskName && repository) {
        if (currentTab === 'existing') {
            const selectedTag = document.querySelector('input[name="selected_tag"]:checked');
            isValid = !!selectedTag;
        } else {
            const selectedBranch = document.querySelector('input[name="branch_name"]:checked');
            const newTagName = document.getElementById('new_tag_name').value.trim();
            isValid = !!(selectedBranch && newTagName);
        }
    }
    
    document.getElementById('submit-btn').disabled = !isValid;
}

// 表单提交
document.getElementById('training-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('task_name', document.getElementById('task_name').value);
    formData.append('repository', selectedRepository);
    formData.append('action_type', currentTab === 'existing' ? 'existing_tag' : 'new_tag');
    
    if (currentTab === 'existing') {
        const selectedTag = document.querySelector('input[name="selected_tag"]:checked');
        formData.append('selected_tag', selectedTag.value);
    } else {
        const selectedBranch = document.querySelector('input[name="branch_name"]:checked');
        formData.append('branch_name', selectedBranch.value);
        formData.append('new_tag_name', document.getElementById('new_tag_name').value);
    }
    
    // 显示加载状态
    document.getElementById('submit-btn').style.display = 'none';
    document.getElementById('loading').style.display = 'block';
    document.getElementById('result-message').style.display = 'none';
    
    fetch('/training_task', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';
        
        // 显示结果
        const resultMessage = document.getElementById('result-message');
        resultMessage.textContent = data.message;
        resultMessage.className = 'result-message success';
        resultMessage.style.display = 'block';
        
        // 重置表单
        setTimeout(() => {
            document.getElementById('training-form').reset();
            selectedRepository = null;
            document.querySelectorAll('.repository-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.getElementById('action-section').style.display = 'none';
            document.getElementById('submit-btn').disabled = true;
            resultMessage.style.display = 'none';
        }, 3000);
    })
    .catch(error => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';
        
        // 显示错误
        const resultMessage = document.getElementById('result-message');
        resultMessage.textContent = '创建训练任务失败，请重试';
        resultMessage.className = 'result-message error';
        resultMessage.style.display = 'block';
        
        console.error('Error:', error);
    });
});

// 监听任务名称输入
document.getElementById('task_name').addEventListener('input', checkFormValidity);
document.getElementById('new_tag_name').addEventListener('input', checkFormValidity);
</script>
{% endblock %}
