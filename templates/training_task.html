{% extends "base.html" %}

{% block title %}创建训练任务{% endblock %}

{% block nav_training_active %}active{% endblock %}

{% block extra_css %}
<style>
    .training-container {
        max-width: 800px;
        background: var(--card-background);
        border-radius: 12px;
        box-shadow: 0 4px 16px var(--shadow-light);
        padding: 40px;
        margin: 20px auto;
    }

    .form-title {
        color: var(--primary-color);
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--card-background);
        color: var(--text-color);
        box-sizing: border-box;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }

    .repositories-section {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 12px;
        padding: 30px;
        margin-top: 20px;
        border: 1px solid var(--border-color);
    }

    .repositories-title {
        color: var(--primary-color);
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .repositories-title::before {
        content: '🔗';
        font-size: 1.6rem;
    }

    .repositories-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
        margin-bottom: 30px;
    }

    .repository-column {
        background-color: var(--card-background);
        border-radius: 12px;
        padding: 25px;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
    }

    .repository-column:hover {
        border-color: var(--primary-color);
        box-shadow: 0 4px 16px var(--shadow-medium);
    }

    .repository-header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
    }

    .repository-name {
        font-weight: 700;
        color: var(--primary-color);
        font-size: 1.3rem;
        margin-bottom: 5px;
    }

    .repository-id {
        font-size: 0.9rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* 每个仓库的工作流选择 */
    .repo-workflow-tabs {
        display: flex;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 3px;
        margin-bottom: 20px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .repo-workflow-tab {
        flex: 1;
        padding: 8px 12px;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 600;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        border-radius: 6px;
        font-size: 0.85rem;
        text-align: center;
    }

    .repo-workflow-tab.active {
        color: white;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        box-shadow: 0 2px 6px rgba(26, 115, 232, 0.3);
    }

    .repo-workflow-tab:hover:not(.active) {
        color: var(--primary-color);
        background-color: rgba(26, 115, 232, 0.08);
    }

    .tag-section {
        margin-bottom: 15px;
    }

    .section-title {
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 12px;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .tag-options, .branch-options {
        max-height: 120px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background-color: #fafafa;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 1px solid #eee;
        font-size: 0.9rem;
    }

    .option-item:last-child {
        border-bottom: none;
    }

    .option-item:hover {
        background-color: rgba(26, 115, 232, 0.05);
    }

    .option-item.selected {
        background-color: rgba(26, 115, 232, 0.1);
        color: var(--primary-color);
        font-weight: 600;
    }

    .option-item input[type="radio"] {
        margin-right: 8px;
        accent-color: var(--primary-color);
    }

    .new-tag-input {
        margin-top: 12px;
    }

    .new-tag-input input {
        width: 100%;
        padding: 8px 10px;
        border: 2px solid var(--border-color);
        border-radius: 6px;
        font-size: 0.85rem;
        transition: border-color 0.3s ease;
        box-sizing: border-box;
    }

    .new-tag-input input:focus {
        outline: none;
        border-color: var(--primary-color);
    }

    .new-tag-input input:disabled {
        background-color: #f5f5f5;
        color: var(--text-secondary);
        cursor: not-allowed;
    }

    .new-tag-input input::placeholder {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .empty-state {
        text-align: center;
        color: var(--text-secondary);
        font-style: italic;
        padding: 15px;
        font-size: 0.85rem;
    }

    .repo-status {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #ddd;
        transition: all 0.3s ease;
    }

    .repo-status.configured {
        background-color: var(--success-color);
        box-shadow: 0 0 0 3px rgba(30, 142, 62, 0.2);
    }

    .repo-status.dependent {
        background-color: #fbbf24;
        box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.2);
    }

    .repo-status.blocked {
        background-color: var(--error-color);
        box-shadow: 0 0 0 3px rgba(217, 48, 37, 0.2);
    }

    /* 依赖关系指示器 */
    .dependency-indicator {
        position: absolute;
        top: 20px;
        right: -15px;
        font-size: 1.5rem;
        color: var(--primary-color);
        z-index: 10;
        background-color: var(--background-color);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .repository-column:last-child .dependency-indicator {
        display: none;
    }

    .dependency-warning {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 15px;
        font-size: 0.85rem;
        color: #92400e;
        display: none;
    }

    .dependency-warning.show {
        display: block;
    }

    .branch-constraint {
        background-color: #e0f2fe;
        border: 1px solid #0288d1;
        border-radius: 6px;
        padding: 8px;
        margin-bottom: 10px;
        font-size: 0.8rem;
        color: #01579b;
        display: none;
    }

    .branch-constraint.show {
        display: block;
    }

    @media (max-width: 1024px) {
        .repositories-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .dependency-indicator {
            display: none;
        }
    }

    .workflow-tabs {
        display: flex;
        background-color: var(--card-background);
        border-radius: 10px;
        padding: 4px;
        margin-bottom: 25px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .workflow-tab {
        flex: 1;
        padding: 12px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 600;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        border-radius: 8px;
        text-align: center;
        font-size: 0.95rem;
    }

    .workflow-tab.active {
        color: white;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    .workflow-tab:hover:not(.active) {
        color: var(--primary-color);
        background-color: rgba(26, 115, 232, 0.08);
    }

    .workflow-description {
        text-align: center;
        color: var(--text-secondary);
        margin-bottom: 25px;
        font-size: 1rem;
        line-height: 1.5;
    }

    .submit-section {
        margin-top: 30px;
        text-align: center;
        padding-top: 20px;
        border-top: 2px solid var(--border-color);
    }

    .submit-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.4);
    }

    .submit-btn:disabled {
        background: var(--text-secondary);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .result-message {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        display: none;
    }

    .result-message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .result-message.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .loading {
        display: none;
        text-align: center;
        margin-top: 20px;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .empty-state {
        text-align: center;
        color: var(--text-secondary);
        font-style: italic;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="training-container">
    <h1 class="form-title">创建训练任务</h1>
    
    <form id="training-form">
        <!-- 任务类型选择 -->
        <div class="form-group">
            <label>任务类型</label>
            <div class="workflow-tabs">
                <button type="button" class="workflow-tab active" onclick="switchTaskType('training')">
                    🚀 训练任务
                </button>
                <button type="button" class="workflow-tab" onclick="switchTaskType('tag_creation')">
                    🏷️ 创建Tag任务
                </button>
            </div>
            <div class="workflow-description">
                <span id="task-type-desc">创建训练任务，三个仓库存在依赖关系：A ← B ← C</span>
            </div>
        </div>

        <!-- 任务名称 -->
        <div class="form-group">
            <label for="task_name">任务名称</label>
            <input type="text" id="task_name" name="task_name" class="form-control"
                   placeholder="请输入任务名称" required>
        </div>

        <!-- 仓库配置区域 -->
        <div class="repositories-section">
            <div class="repositories-title">
                仓库依赖配置
            </div>
            <div id="dependency-description" style="text-align: center; color: var(--text-secondary); margin-bottom: 25px; font-size: 1rem;">
                训练任务依赖关系：A ← B ← C（分支名需保持一致）
            </div>

            <div class="repositories-grid">
                {% for repo_id, repo_info in repositories.items() %}
                <div class="repository-column" id="repo-column-{{ repo_id }}">
                    <div class="repo-status" id="repo-status-{{ repo_id }}"></div>
                    {% if repo_id != 'C' %}
                    <div class="dependency-indicator">→</div>
                    {% endif %}

                    <div class="repository-header">
                        <div class="repository-name">{{ repo_info.name }}</div>
                        <div class="repository-id">仓库{{ repo_id }}</div>
                    </div>

                    <!-- 依赖关系警告 -->
                    <div class="dependency-warning" id="dependency-warning-{{ repo_id }}">
                        <strong>依赖约束：</strong><span id="dependency-text-{{ repo_id }}"></span>
                    </div>

                    <!-- 分支约束提示 -->
                    <div class="branch-constraint" id="branch-constraint-{{ repo_id }}">
                        <strong>分支约束：</strong><span id="branch-constraint-text-{{ repo_id }}"></span>
                    </div>

                    <!-- 每个仓库的工作流选择 -->
                    <div class="repo-workflow-tabs" id="repo-workflow-tabs-{{ repo_id }}">
                        <button type="button" class="repo-workflow-tab active"
                                onclick="switchRepoWorkflow('{{ repo_id }}', 'existing')">
                            🏷️ 已有标签
                        </button>
                        <button type="button" class="repo-workflow-tab"
                                onclick="switchRepoWorkflow('{{ repo_id }}', 'branch')" id="branch-tab-{{ repo_id }}">
                            🌿 选择分支
                        </button>
                    </div>

                    <!-- 已有标签选择 -->
                    <div class="tag-section" id="existing-section-{{ repo_id }}">
                        <div class="section-title">
                            🏷️ 选择标签
                        </div>
                        <div class="tag-options" id="tag-options-{{ repo_id }}">
                            <div class="empty-state">加载中...</div>
                        </div>
                    </div>

                    <!-- 分支选择 -->
                    <div class="tag-section" id="branch-section-{{ repo_id }}" style="display: none;">
                        <div class="section-title">
                            🌿 选择分支进行训练
                        </div>
                        <div class="branch-options" id="branch-options-{{ repo_id }}">
                            <div class="empty-state">加载中...</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
            <button type="submit" class="submit-btn" id="submit-btn" disabled>
                创建训练任务
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>正在创建训练任务...</div>
            </div>
            
            <div class="result-message" id="result-message"></div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let repositoryData = {};
let selectedTags = {};
let selectedBranches = {};
let newTagNames = {};
let repoWorkflows = { A: 'existing', B: 'existing', C: 'existing' }; // 每个仓库的工作流状态：existing 或 branch
let currentTaskType = 'training'; // 当前任务类型：training 或 tag_creation
let dependencyChain = ['A', 'B', 'C']; // 依赖链：A <- B <- C

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载所有仓库信息
    loadAllRepositories();

    // 为所有新标签输入框添加事件监听
    ['A', 'B', 'C'].forEach(repoId => {
        const input = document.getElementById(`new-tag-${repoId}`);
        if (input) {
            input.addEventListener('input', function() {
                newTagNames[repoId] = this.value.trim();
                updateRepoStatus(repoId);
                updateDependencyConstraints();
                checkFormValidity();
            });
        }
    });
});

// 切换任务类型
function switchTaskType(taskType) {
    // 更新任务类型标签页状态
    document.querySelectorAll('.workflow-tab').forEach(t => t.classList.remove('active'));
    event.target.classList.add('active');

    currentTaskType = taskType;

    // 更新描述文本
    const taskTypeDesc = document.getElementById('task-type-desc');
    const dependencyDesc = document.getElementById('dependency-description');

    if (taskType === 'training') {
        taskTypeDesc.textContent = '创建训练任务，三个仓库存在依赖关系：A ← B ← C';
        dependencyDesc.textContent = '训练任务依赖关系：A ← B ← C（分支名需保持一致）';

        // 更新分支标签页文案
        ['A', 'B', 'C'].forEach(repoId => {
            const branchTab = document.getElementById(`branch-tab-${repoId}`);
            if (branchTab) {
                branchTab.innerHTML = '🌿 选择分支';
            }
        });

        // 启用依赖关系约束
        enableDependencyConstraints();
    } else {
        taskTypeDesc.textContent = '创建Tag任务，每个仓库可以独立选择标签或分支';
        dependencyDesc.textContent = '每个仓库可以独立选择使用已有标签或创建新标签';

        // 更新分支标签页文案
        ['A', 'B', 'C'].forEach(repoId => {
            const branchTab = document.getElementById(`branch-tab-${repoId}`);
            if (branchTab) {
                branchTab.innerHTML = '✨ 创建标签';
            }
        });

        // 禁用依赖关系约束
        disableDependencyConstraints();
    }

    // 重置所有选择
    resetAllSelections();

    // 检查表单完整性
    checkFormValidity();
}

// 加载所有仓库信息
function loadAllRepositories() {
    const repositories = ['A', 'B', 'C'];

    repositories.forEach(repoId => {
        fetch(`/api/repository/${repoId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error(`Error loading repository ${repoId}:`, data.error);
                    return;
                }

                // 保存仓库数据
                repositoryData[repoId] = data;

                // 更新对应仓库的选项
                updateRepositoryOptions(repoId, data);
            })
            .catch(error => {
                console.error(`Error loading repository ${repoId}:`, error);
            });
    });
}

// 更新仓库选项
function updateRepositoryOptions(repoId, data) {
    // 更新标签选项
    updateTagOptionsForRepo(repoId, data.tags);

    // 更新分支选项
    updateBranchOptionsForRepo(repoId, data.branches);
}

// 更新特定仓库的标签选项
function updateTagOptionsForRepo(repoId, tags) {
    const tagOptions = document.getElementById(`tag-options-${repoId}`);

    if (tags.length === 0) {
        tagOptions.innerHTML = '<div class="empty-state">该仓库暂无可用标签</div>';
        return;
    }

    tagOptions.innerHTML = tags.map(tag => `
        <div class="option-item" onclick="selectTag('${repoId}', '${tag}', this)">
            <input type="radio" name="tag_${repoId}" value="${tag}" id="tag_${repoId}_${tag}">
            <label for="tag_${repoId}_${tag}">${tag}</label>
        </div>
    `).join('');
}

// 更新特定仓库的分支选项
function updateBranchOptionsForRepo(repoId, branches) {
    const branchOptions = document.getElementById(`branch-options-${repoId}`);

    if (branches.length === 0) {
        branchOptions.innerHTML = '<div class="empty-state">该仓库暂无可用分支</div>';
        return;
    }

    branchOptions.innerHTML = branches.map(branch => `
        <div class="option-item" onclick="selectBranch('${repoId}', '${branch}', this)">
            <input type="radio" name="branch_${repoId}" value="${branch}" id="branch_${repoId}_${branch}">
            <label for="branch_${repoId}_${branch}">${branch}</label>
        </div>
    `).join('');
}

// 启用依赖关系约束
function enableDependencyConstraints() {
    // 显示依赖关系指示器
    document.querySelectorAll('.dependency-indicator').forEach(indicator => {
        indicator.style.display = 'block';
    });

    // 更新依赖约束
    updateDependencyConstraints();
}

// 禁用依赖关系约束
function disableDependencyConstraints() {
    // 隐藏依赖关系指示器
    document.querySelectorAll('.dependency-indicator').forEach(indicator => {
        indicator.style.display = 'none';
    });

    // 隐藏所有依赖警告和约束
    ['A', 'B', 'C'].forEach(repoId => {
        document.getElementById(`dependency-warning-${repoId}`).classList.remove('show');
        document.getElementById(`branch-constraint-${repoId}`).classList.remove('show');

        // 启用所有工作流选择
        const tabs = document.getElementById(`repo-workflow-tabs-${repoId}`);
        tabs.querySelectorAll('.repo-workflow-tab').forEach(tab => {
            tab.disabled = false;
            tab.style.opacity = '1';
        });
    });
}

// 切换仓库工作流
function switchRepoWorkflow(repoId, workflow) {
    // 检查训练任务的依赖约束
    if (currentTaskType === 'training' && !canSwitchWorkflow(repoId, workflow)) {
        return;
    }

    // 更新该仓库的工作流标签页状态
    const repoColumn = document.getElementById(`repo-column-${repoId}`);
    const tabs = repoColumn.querySelectorAll('.repo-workflow-tab');
    tabs.forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');

    // 更新仓库工作流状态
    repoWorkflows[repoId] = workflow;

    // 显示/隐藏对应的区域
    const existingSection = document.getElementById(`existing-section-${repoId}`);
    const branchSection = document.getElementById(`branch-section-${repoId}`);

    if (workflow === 'existing') {
        existingSection.style.display = 'block';
        branchSection.style.display = 'none';

        // 清除该仓库的分支选择
        delete selectedBranches[repoId];

        // 清除分支选择状态
        branchSection.querySelectorAll('.option-item').forEach(item => {
            item.classList.remove('selected');
        });
        branchSection.querySelectorAll('input[type="radio"]').forEach(input => {
            input.checked = false;
        });
    } else if (workflow === 'branch') {
        existingSection.style.display = 'none';
        branchSection.style.display = 'block';

        // 清除该仓库的标签选择
        delete selectedTags[repoId];

        // 清除标签选择状态
        existingSection.querySelectorAll('.option-item').forEach(item => {
            item.classList.remove('selected');
        });
        existingSection.querySelectorAll('input[type="radio"]').forEach(input => {
            input.checked = false;
        });

        // 确保分支选项已加载
        if (repositoryData[repoId] && repositoryData[repoId].branches) {
            updateBranchOptionsForRepo(repoId, repositoryData[repoId].branches);
        }
    }

    // 更新仓库状态和依赖约束
    updateRepoStatus(repoId);
    if (currentTaskType === 'training') {
        // 清除后续仓库的依赖约束和警告
        clearDownstreamConstraints(repoId);

        // 重新计算所有依赖约束
        updateDependencyConstraints();

        // 如果选择了分支，需要显示警告给后续仓库
        if (workflow === 'branch') {
            propagateBranchConstraint(repoId);
        }
    }

    // 检查表单完整性
    checkFormValidity();
}

// 检查是否可以切换工作流（训练任务依赖约束）
function canSwitchWorkflow(repoId, workflow) {
    if (currentTaskType !== 'training') return true;

    const repoIndex = dependencyChain.indexOf(repoId);

    // 检查前置依赖仓库的状态
    for (let i = 0; i < repoIndex; i++) {
        const depRepoId = dependencyChain[i];
        const depWorkflow = repoWorkflows[depRepoId];

        if (workflow === 'existing' && depWorkflow === 'branch') {
            // 如果前置仓库选择了分支训练，当前仓库不能选择已有标签
            showDependencyWarning(repoId, `仓库${depRepoId}选择了分支进行训练，当前仓库必须也选择分支以保持一致性`);
            return false;
        }
    }

    return true;
}

// 清除下游仓库的约束和警告
function clearDownstreamConstraints(sourceRepoId) {
    const sourceIndex = dependencyChain.indexOf(sourceRepoId);

    // 清除所有后续仓库的约束
    for (let i = sourceIndex + 1; i < dependencyChain.length; i++) {
        const targetRepoId = dependencyChain[i];

        // 清除约束提示
        const constraintDiv = document.getElementById(`branch-constraint-${targetRepoId}`);
        constraintDiv.classList.remove('show');

        // 清除警告信息
        const warningDiv = document.getElementById(`dependency-warning-${targetRepoId}`);
        if (warningDiv) {
            warningDiv.classList.remove('show');
        }

        // 恢复工作流选项的可用性
        const workflowTabs = document.getElementById(`repo-workflow-tabs-${targetRepoId}`);
        const tabs = workflowTabs.querySelectorAll('.repo-workflow-tab');
        tabs.forEach(tab => {
            tab.disabled = false;
            tab.style.opacity = '1';
        });

        // 如果当前仓库在分支模式，恢复所有分支选项
        if (repoWorkflows[targetRepoId] === 'branch' && repositoryData[targetRepoId]) {
            updateBranchOptionsForRepo(targetRepoId, repositoryData[targetRepoId].branches);
        }
    }
}

// 传播分支约束到后续仓库（简化版本）
function propagateBranchConstraint(sourceRepoId) {
    const sourceIndex = dependencyChain.indexOf(sourceRepoId);

    // 对所有后续仓库显示警告
    for (let i = sourceIndex + 1; i < dependencyChain.length; i++) {
        const targetRepoId = dependencyChain[i];

        // 显示警告信息
        showDependencyWarning(targetRepoId, `仓库${sourceRepoId}选择了分支进行训练，后续仓库将以同名分支进行训练，若分支不存在则会从master复制分支`);
    }
}

// 显示依赖警告
function showDependencyWarning(repoId, message) {
    const warning = document.getElementById(`dependency-warning-${repoId}`);
    document.getElementById(`dependency-text-${repoId}`).textContent = message;
    warning.classList.add('show');

    setTimeout(() => {
        warning.classList.remove('show');
    }, 8000); // 延长显示时间
}

// 选择标签
function selectTag(repoId, tag, element) {
    // 移除同仓库其他标签的选中状态
    const container = element.parentElement;
    container.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    element.classList.add('selected');
    element.querySelector('input[type="radio"]').checked = true;

    // 保存选择
    selectedTags[repoId] = tag;

    // 更新仓库状态和依赖约束
    updateRepoStatus(repoId);
    if (currentTaskType === 'training') {
        // 清除下游约束
        clearDownstreamConstraints(repoId);

        // 重新计算依赖约束
        updateDependencyConstraints();
    }

    // 检查表单完整性
    checkFormValidity();
}

// 选择分支
function selectBranch(repoId, branch, element) {
    // 获取分支选项容器
    const branchOptions = document.getElementById(`branch-options-${repoId}`);

    // 移除同仓库其他分支的选中状态
    branchOptions.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });
    branchOptions.querySelectorAll('input[type="radio"]').forEach(input => {
        input.checked = false;
    });

    // 添加选中状态
    element.classList.add('selected');
    const radioInput = element.querySelector('input[type="radio"]');
    if (radioInput) {
        radioInput.checked = true;
    }

    // 保存选择
    selectedBranches[repoId] = branch;

    // 更新仓库状态
    updateRepoStatus(repoId);

    // 如果是训练任务，需要实时更新依赖约束
    if (currentTaskType === 'training') {
        // 清除下游约束
        clearDownstreamConstraints(repoId);

        // 重新计算依赖约束
        updateDependencyConstraints();

        // 传播分支约束到后续仓库
        propagateBranchSelectionConstraint(repoId, branch);
    }

    // 检查表单完整性
    checkFormValidity();
}

// 传播分支选择约束到后续仓库
function propagateBranchSelectionConstraint(sourceRepoId, selectedBranch) {
    const sourceIndex = dependencyChain.indexOf(sourceRepoId);

    // 对所有后续仓库应用分支约束
    for (let i = sourceIndex + 1; i < dependencyChain.length; i++) {
        const targetRepoId = dependencyChain[i];

        // 显示警告信息
        showDependencyWarning(targetRepoId, `仓库${sourceRepoId}选择了分支进行训练，后续仓库将以同名分支进行训练，若分支不存在则会从master复制分支`);

        // 强制切换到分支模式
        const repoColumn = document.getElementById(`repo-column-${targetRepoId}`);
        const tabs = repoColumn.querySelectorAll('.repo-workflow-tab');
        tabs[0].classList.remove('active');
        tabs[1].classList.add('active');
        tabs[0].disabled = true;
        tabs[0].style.opacity = '0.5';
        tabs[1].disabled = false;
        tabs[1].style.opacity = '1';

        repoWorkflows[targetRepoId] = 'branch';

        // 切换显示区域
        document.getElementById(`existing-section-${targetRepoId}`).style.display = 'none';
        document.getElementById(`branch-section-${targetRepoId}`).style.display = 'block';

        // 清除标签选择
        delete selectedTags[targetRepoId];
        delete selectedBranches[targetRepoId]; // 清除之前的分支选择

        // 显示分支约束提示
        const constraintDiv = document.getElementById(`branch-constraint-${targetRepoId}`);
        const constraintText = document.getElementById(`branch-constraint-text-${targetRepoId}`);
        constraintText.textContent = `必须使用分支 "${selectedBranch}" 以保持依赖一致性`;
        constraintDiv.classList.add('show');

        // 设置分支选项（不自动选择）
        setBranchOptionsForConstraint(targetRepoId, selectedBranch);

        // 更新状态
        updateRepoStatus(targetRepoId);
    }
}

// 更新依赖约束
function updateDependencyConstraints() {
    if (currentTaskType !== 'training') return;

    dependencyChain.forEach((repoId, index) => {
        if (index === 0) return; // A仓库没有约束

        const workflowTabs = document.getElementById(`repo-workflow-tabs-${repoId}`);
        const tabs = workflowTabs.querySelectorAll('.repo-workflow-tab');
        const constraintDiv = document.getElementById(`branch-constraint-${repoId}`);
        const constraintText = document.getElementById(`branch-constraint-text-${repoId}`);

        // 重置约束显示
        constraintDiv.classList.remove('show');

        // 检查前置仓库的状态
        const prevRepoId = dependencyChain[index - 1];
        const prevWorkflow = repoWorkflows[prevRepoId];

        if (prevWorkflow === 'branch' && selectedBranches[prevRepoId]) {
            // 前置仓库选择了分支，当前仓库必须使用相同分支
            const requiredBranch = selectedBranches[prevRepoId];

            // 显示约束提示
            constraintText.textContent = `必须使用分支 "${requiredBranch}" 以保持依赖一致性`;
            constraintDiv.classList.add('show');

            // 禁用"已有标签"选项
            tabs[0].disabled = true;
            tabs[0].style.opacity = '0.5';
            tabs[1].disabled = false;
            tabs[1].style.opacity = '1';

            // 如果当前仓库还在"已有标签"模式，自动切换到"选择分支"
            if (repoWorkflows[repoId] === 'existing') {
                tabs[0].classList.remove('active');
                tabs[1].classList.add('active');
                repoWorkflows[repoId] = 'branch';

                // 切换显示区域
                document.getElementById(`existing-section-${repoId}`).style.display = 'none';
                document.getElementById(`branch-section-${repoId}`).style.display = 'block';

                // 清除标签选择
                delete selectedTags[repoId];

                // 确保分支选项已加载
                if (repositoryData[repoId] && repositoryData[repoId].branches) {
                    updateBranchOptionsForRepo(repoId, repositoryData[repoId].branches);
                }
            }

            // 设置约束的分支选项
            setBranchOptionsForConstraint(repoId, requiredBranch);

        } else {
            // 前置仓库选择了标签或还没配置，当前仓库可以自由选择
            tabs.forEach(tab => {
                tab.disabled = false;
                tab.style.opacity = '1';
            });

            // 恢复所有分支选项
            if (repoWorkflows[repoId] === 'branch' && repositoryData[repoId]) {
                updateBranchOptionsForRepo(repoId, repositoryData[repoId].branches);
            }
        }
    });
}

// 设置分支选项（用于约束情况，不自动选择）
function setBranchOptionsForConstraint(repoId, requiredBranch) {
    const branchOptions = document.getElementById(`branch-options-${repoId}`);
    const allBranches = repositoryData[repoId]?.branches || [];

    if (allBranches.includes(requiredBranch)) {
        // 如果存在匹配的分支，只显示该分支
        branchOptions.innerHTML = `
            <div class="option-item" onclick="selectBranch('${repoId}', '${requiredBranch}', this)">
                <input type="radio" name="branch_${repoId}" value="${requiredBranch}" id="branch_${repoId}_${requiredBranch}">
                <label for="branch_${repoId}_${requiredBranch}">${requiredBranch} (依赖要求)</label>
            </div>
        `;
    } else {
        // 如果不存在匹配的分支，显示会从master复制的提示
        branchOptions.innerHTML = `
            <div class="option-item" onclick="selectBranch('${repoId}', '${requiredBranch}', this)">
                <input type="radio" name="branch_${repoId}" value="${requiredBranch}" id="branch_${repoId}_${requiredBranch}">
                <label for="branch_${repoId}_${requiredBranch}">${requiredBranch} (将从master复制创建)</label>
            </div>
            <div class="empty-state" style="color: #f59e0b; font-size: 0.8rem; padding: 8px;">
                注意：该仓库没有分支 "${requiredBranch}"，系统将从master分支复制创建
            </div>
        `;
    }
}

// 过滤分支选项（保留原有逻辑但移除自动选择）
function filterBranchOptions(repoId, requiredBranch) {
    setBranchOptionsForConstraint(repoId, requiredBranch);
}

// 恢复分支选项
function restoreBranchOptions(repoId) {
    const branches = repositoryData[repoId]?.branches || [];
    updateBranchOptionsForRepo(repoId, branches);
}

// 更新仓库状态指示器
function updateRepoStatus(repoId) {
    const statusIndicator = document.getElementById(`repo-status-${repoId}`);
    const workflow = repoWorkflows[repoId];

    let isConfigured = false;
    let status = 'default';

    if (workflow === 'existing') {
        isConfigured = !!selectedTags[repoId];
    } else {
        isConfigured = !!(selectedBranches[repoId] && newTagNames[repoId]);
    }

    // 检查依赖状态
    if (currentTaskType === 'training') {
        const repoIndex = dependencyChain.indexOf(repoId);
        if (repoIndex > 0) {
            const prevRepoId = dependencyChain[repoIndex - 1];
            const prevConfigured = repoWorkflows[prevRepoId] === 'existing' ?
                !!selectedTags[prevRepoId] :
                !!(selectedBranches[prevRepoId] && newTagNames[prevRepoId]);

            if (!prevConfigured) {
                status = 'blocked';
            } else if (!isConfigured) {
                status = 'dependent';
            }
        }
    }

    // 更新状态指示器
    statusIndicator.classList.remove('configured', 'dependent', 'blocked');
    if (isConfigured && status !== 'blocked') {
        statusIndicator.classList.add('configured');
    } else if (status === 'dependent') {
        statusIndicator.classList.add('dependent');
    } else if (status === 'blocked') {
        statusIndicator.classList.add('blocked');
    }
}

// 重置所有选择
function resetAllSelections() {
    // 清除选中状态
    document.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 清除单选框选中状态
    document.querySelectorAll('input[type="radio"]').forEach(input => {
        if (input.name.startsWith('tag_') || input.name.startsWith('branch_')) {
            input.checked = false;
        }
    });

    // 清除新标签输入框
    document.querySelectorAll('[id^="new-tag-"]').forEach(input => {
        input.value = '';
        input.disabled = true;
    });

    // 重置数据
    selectedTags = {};
    selectedBranches = {};
    newTagNames = {};
    repoWorkflows = { A: 'existing', B: 'existing', C: 'existing' };

    // 重置所有仓库的工作流标签页
    ['A', 'B', 'C'].forEach(repoId => {
        const repoColumn = document.getElementById(`repo-column-${repoId}`);
        const tabs = repoColumn.querySelectorAll('.repo-workflow-tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
            tab.disabled = false;
            tab.style.opacity = '1';
        });
        tabs[0].classList.add('active'); // 激活第一个标签页（已有标签）

        // 显示标签选择，隐藏分支选择
        document.getElementById(`existing-section-${repoId}`).style.display = 'block';
        document.getElementById(`branch-section-${repoId}`).style.display = 'none';

        // 隐藏依赖警告和约束
        document.getElementById(`dependency-warning-${repoId}`).classList.remove('show');
        document.getElementById(`branch-constraint-${repoId}`).classList.remove('show');

        // 恢复分支选项
        restoreBranchOptions(repoId);

        // 更新状态指示器
        updateRepoStatus(repoId);
    });
}

// 清除所有选择（保持当前工作流状态）
function clearAllSelections() {
    resetAllSelections();
}

// 检查表单完整性
function checkFormValidity() {
    const taskName = document.getElementById('task_name').value.trim();
    let isValid = false;

    if (taskName) {
        // 检查是否所有仓库都已配置
        const repositories = ['A', 'B', 'C'];
        isValid = repositories.every(repoId => {
            const workflow = repoWorkflows[repoId];
            if (workflow === 'existing') {
                return !!selectedTags[repoId];
            } else if (workflow === 'branch') {
                // 训练任务只需要选择分支，不需要新标签名称
                if (currentTaskType === 'training') {
                    return !!selectedBranches[repoId];
                } else {
                    // Tag创建任务需要分支和新标签名称
                    return !!(selectedBranches[repoId] && newTagNames[repoId]);
                }
            }
            return false;
        });
    }

    document.getElementById('submit-btn').disabled = !isValid;
}

// 构建提交数据
function buildSubmitData() {
    const taskName = document.getElementById('task_name').value.trim();

    const baseData = {
        task_name: taskName,
        task_type: currentTaskType,
        timestamp: new Date().toISOString(),
        repositories: {}
    };

    // 添加依赖关系信息（仅训练任务）
    if (currentTaskType === 'training') {
        baseData.dependency_chain = dependencyChain;
        baseData.dependency_rules = {
            branch_consistency: true,
            execution_order: dependencyChain
        };
    }

    const repositories = ['A', 'B', 'C'];

    repositories.forEach(repoId => {
        const workflow = repoWorkflows[repoId];

        if (workflow === 'existing') {
            baseData.repositories[repoId] = {
                id: repoId,
                name: repositoryData[repoId]?.name || `仓库${repoId}`,
                workflow: 'existing',
                action: 'use_existing_tag',
                tag: {
                    name: selectedTags[repoId],
                    source: 'existing'
                }
            };
        } else if (workflow === 'branch') {
            const repoData = {
                id: repoId,
                name: repositoryData[repoId]?.name || `仓库${repoId}`,
                workflow: 'branch',
                branch: {
                    name: selectedBranches[repoId],
                    source: 'branch'
                }
            };

            if (currentTaskType === 'training') {
                // 训练任务：使用分支进行训练
                repoData.action = 'train_from_branch';
            } else {
                // Tag创建任务：从分支创建新标签
                repoData.action = 'create_new_tag';
                repoData.tag = {
                    name: newTagNames[repoId],
                    source: 'new',
                    base_branch: selectedBranches[repoId]
                };
            }

            baseData.repositories[repoId] = repoData;
        }

        // 添加依赖信息（仅训练任务）
        if (currentTaskType === 'training') {
            const repoIndex = dependencyChain.indexOf(repoId);
            baseData.repositories[repoId].dependency_index = repoIndex;
            baseData.repositories[repoId].depends_on = repoIndex > 0 ? dependencyChain[repoIndex - 1] : null;

            // 如果是分支训练，添加分支约束信息
            if (workflow === 'branch') {
                baseData.repositories[repoId].branch_constraint = {
                    required_branch: selectedBranches[repoId],
                    create_if_missing: true,
                    copy_from: 'master'
                };
            }
        }
    });

    return baseData;
}

// 表单提交
document.getElementById('training-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // 构建提交数据
    const submitData = buildSubmitData();

    // 显示加载状态
    document.getElementById('submit-btn').style.display = 'none';
    document.getElementById('loading').style.display = 'block';
    document.getElementById('result-message').style.display = 'none';

    // 发送请求
    fetch('/training_task', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData)
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';

        // 显示结果
        const resultMessage = document.getElementById('result-message');
        if (data.success !== false) {
            resultMessage.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 8px;">✅ ${data.message}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">
                    仓库: ${data.repository?.name || data.repository} |
                    ${data.action === 'use_existing_tag' ? `标签: ${data.tag}` : `分支: ${data.branch} → 新标签: ${data.new_tag}`}
                </div>
            `;
            resultMessage.className = 'result-message success';
        } else {
            resultMessage.textContent = data.message || '创建训练任务失败';
            resultMessage.className = 'result-message error';
        }
        resultMessage.style.display = 'block';

        // 成功后重置表单
        if (data.success !== false) {
            setTimeout(() => {
                resetForm();
            }, 3000);
        }
    })
    .catch(error => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';

        // 显示错误
        const resultMessage = document.getElementById('result-message');
        resultMessage.textContent = '网络错误，请检查连接后重试';
        resultMessage.className = 'result-message error';
        resultMessage.style.display = 'block';

        console.error('Error:', error);
    });
});

// 重置表单
function resetForm() {
    document.getElementById('training-form').reset();

    // 重置数据
    selectedTags = {};
    selectedBranches = {};
    newTagNames = {};
    repoWorkflows = { A: 'existing', B: 'existing', C: 'existing' };

    // 重置UI状态
    clearAllSelections();

    // 重置所有仓库的工作流标签页
    ['A', 'B', 'C'].forEach(repoId => {
        const repoColumn = document.getElementById(`repo-column-${repoId}`);
        const tabs = repoColumn.querySelectorAll('.repo-workflow-tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        tabs[0].classList.add('active'); // 激活第一个标签页（已有标签）

        // 显示标签选择，隐藏分支选择
        document.getElementById(`existing-section-${repoId}`).style.display = 'block';
        document.getElementById(`branch-section-${repoId}`).style.display = 'none';

        // 更新状态指示器
        updateRepoStatus(repoId);
    });

    // 禁用提交按钮
    document.getElementById('submit-btn').disabled = true;

    // 隐藏结果消息
    document.getElementById('result-message').style.display = 'none';
}

// 监听任务名称输入
document.getElementById('task_name').addEventListener('input', checkFormValidity);
</script>
{% endblock %}
