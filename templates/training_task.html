{% extends "base.html" %}

{% block title %}创建训练任务{% endblock %}

{% block nav_training_active %}active{% endblock %}

{% block extra_css %}
<style>
    .training-container {
        max-width: 800px;
        background: var(--card-background);
        border-radius: 12px;
        box-shadow: 0 4px 16px var(--shadow-light);
        padding: 40px;
        margin: 20px auto;
    }

    .form-title {
        color: var(--primary-color);
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 30px;
        text-align: center;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 8px;
        font-size: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--card-background);
        color: var(--text-color);
        box-sizing: border-box;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }

    .selection-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 15px;
    }

    .selection-card {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: var(--card-background);
        position: relative;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .selection-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px var(--shadow-medium);
    }

    .selection-card.selected {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, rgba(26, 115, 232, 0.08), rgba(26, 115, 232, 0.03));
        box-shadow: 0 4px 16px rgba(26, 115, 232, 0.2);
    }

    .selection-card input[type="radio"] {
        display: none;
    }

    .card-header {
        text-align: center;
        margin-bottom: 15px;
    }

    .card-title {
        font-weight: 700;
        color: var(--primary-color);
        font-size: 1.2rem;
        margin-bottom: 5px;
    }

    .card-subtitle {
        font-size: 0.9rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .card-icon {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        background-color: var(--card-background);
        transition: all 0.3s ease;
    }

    .selection-card.selected .card-icon {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .selection-card.selected .card-icon::after {
        content: '✓';
        color: white;
        font-size: 14px;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .workflow-section {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-radius: 12px;
        padding: 30px;
        margin-top: 30px;
        border: 1px solid var(--border-color);
    }

    .workflow-title {
        color: var(--primary-color);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .workflow-title::before {
        content: '⚙️';
        font-size: 1.5rem;
    }

    .workflow-tabs {
        display: flex;
        background-color: var(--card-background);
        border-radius: 10px;
        padding: 4px;
        margin-bottom: 25px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    }

    .workflow-tab {
        flex: 1;
        padding: 12px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 600;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        border-radius: 8px;
        position: relative;
    }

    .workflow-tab.active {
        color: white;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    .workflow-tab:hover:not(.active) {
        color: var(--primary-color);
        background-color: rgba(26, 115, 232, 0.08);
    }

    .workflow-content {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }

    .workflow-content.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .options-container {
        background-color: var(--card-background);
        border-radius: 10px;
        padding: 20px;
        border: 1px solid var(--border-color);
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        margin: 8px 0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        background-color: #fafafa;
        position: relative;
    }

    .option-item:hover {
        background-color: rgba(26, 115, 232, 0.05);
        border-color: rgba(26, 115, 232, 0.2);
        transform: translateX(5px);
    }

    .option-item.selected {
        background: linear-gradient(135deg, rgba(26, 115, 232, 0.1), rgba(26, 115, 232, 0.05));
        border-color: var(--primary-color);
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15);
    }

    .option-item input[type="radio"] {
        display: none;
    }

    .option-content {
        flex: 1;
        margin-left: 15px;
    }

    .option-title {
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 4px;
        font-size: 1rem;
    }

    .option-description {
        font-size: 0.9rem;
        color: var(--text-secondary);
        line-height: 1.4;
    }

    .option-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        background-color: var(--card-background);
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .option-item.selected .option-icon {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .option-item.selected .option-icon::after {
        content: '✓';
        color: white;
        font-size: 12px;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .input-group {
        margin-top: 20px;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }

    .submit-section {
        margin-top: 30px;
        text-align: center;
        padding-top: 20px;
        border-top: 2px solid var(--border-color);
    }

    .submit-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.4);
    }

    .submit-btn:disabled {
        background: var(--text-secondary);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .result-message {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        display: none;
    }

    .result-message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .result-message.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .loading {
        display: none;
        text-align: center;
        margin-top: 20px;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .empty-state {
        text-align: center;
        color: var(--text-secondary);
        font-style: italic;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="training-container">
    <h1 class="form-title">创建训练任务</h1>
    
    <form id="training-form">
        <!-- 任务名称 -->
        <div class="form-group">
            <label for="task_name">任务名称</label>
            <input type="text" id="task_name" name="task_name" class="form-control" 
                   placeholder="请输入训练任务名称" required>
        </div>

        <!-- 仓库选择 -->
        <div class="form-group">
            <label>选择仓库</label>
            <div class="selection-grid">
                {% for repo_id, repo_info in repositories.items() %}
                <div class="selection-card" onclick="selectRepository('{{ repo_id }}')">
                    <input type="radio" name="repository" value="{{ repo_id }}" id="repo_{{ repo_id }}">
                    <div class="card-icon"></div>
                    <div class="card-header">
                        <div class="card-title">{{ repo_info.name }}</div>
                        <div class="card-subtitle">仓库{{ repo_id }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 工作流选择 -->
        <div class="workflow-section" id="workflow-section" style="display: none;">
            <div class="workflow-title">
                选择工作流程
            </div>

            <div class="workflow-tabs">
                <button type="button" class="workflow-tab active" onclick="switchWorkflow('existing')">
                    🏷️ 使用已有标签
                </button>
                <button type="button" class="workflow-tab" onclick="switchWorkflow('new')">
                    ✨ 创建新标签
                </button>
            </div>

            <!-- 使用已有标签工作流 -->
            <div class="workflow-content active" id="existing-workflow">
                <div class="options-container">
                    <div class="form-group">
                        <label style="margin-bottom: 15px; font-size: 1.1rem;">📋 选择标签</label>
                        <div id="tag-options">
                            <div class="empty-state">请先选择仓库</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建新标签工作流 -->
            <div class="workflow-content" id="new-workflow">
                <div class="options-container">
                    <div class="form-group">
                        <label style="margin-bottom: 15px; font-size: 1.1rem;">🌿 选择分支</label>
                        <div id="branch-options">
                            <div class="empty-state">请先选择仓库</div>
                        </div>
                    </div>

                    <div class="input-group" id="new-tag-group" style="display: none;">
                        <label for="new_tag_name" style="font-weight: 600; margin-bottom: 10px; display: block;">
                            🏷️ 新标签名称
                        </label>
                        <input type="text" id="new_tag_name" name="new_tag_name" class="form-control"
                               placeholder="请输入新标签名称（如：v1.0.0）" style="font-size: 1rem;">
                        <div style="margin-top: 8px; font-size: 0.9rem; color: var(--text-secondary);">
                            💡 建议使用语义化版本号格式，如：v1.0.0, v2.1.3
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
            <button type="submit" class="submit-btn" id="submit-btn" disabled>
                创建训练任务
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>正在创建训练任务...</div>
            </div>
            
            <div class="result-message" id="result-message"></div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedRepository = null;
let currentWorkflow = 'existing';
let repositoryData = {};

// 选择仓库
function selectRepository(repoId) {
    // 移除之前的选中状态
    document.querySelectorAll('.selection-card').forEach(card => {
        card.classList.remove('selected');
    });

    // 添加选中状态
    event.currentTarget.classList.add('selected');
    document.getElementById(`repo_${repoId}`).checked = true;

    selectedRepository = repoId;

    // 显示工作流区域
    document.getElementById('workflow-section').style.display = 'block';

    // 加载仓库信息
    loadRepositoryInfo(repoId);

    // 检查表单完整性
    checkFormValidity();
}

// 加载仓库信息
function loadRepositoryInfo(repoId) {
    fetch(`/api/repository/${repoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            // 保存仓库数据
            repositoryData[repoId] = data;

            // 更新标签选项
            updateTagOptions(data.tags);

            // 更新分支选项
            updateBranchOptions(data.branches);
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// 更新标签选项
function updateTagOptions(tags) {
    const tagOptions = document.getElementById('tag-options');

    if (tags.length === 0) {
        tagOptions.innerHTML = '<div class="empty-state">该仓库暂无可用标签</div>';
        return;
    }

    tagOptions.innerHTML = tags.map(tag => `
        <div class="option-item" onclick="selectOption(this, 'tag', '${tag}')">
            <div class="option-icon"></div>
            <input type="radio" name="selected_tag" value="${tag}" id="tag_${tag}">
            <div class="option-content">
                <div class="option-title">${tag}</div>
                <div class="option-description">使用此标签创建训练任务</div>
            </div>
        </div>
    `).join('');
}

// 更新分支选项
function updateBranchOptions(branches) {
    const branchOptions = document.getElementById('branch-options');

    if (branches.length === 0) {
        branchOptions.innerHTML = '<div class="empty-state">该仓库暂无可用分支</div>';
        return;
    }

    branchOptions.innerHTML = branches.map(branch => `
        <div class="option-item" onclick="selectOption(this, 'branch', '${branch}')">
            <div class="option-icon"></div>
            <input type="radio" name="branch_name" value="${branch}" id="branch_${branch}">
            <div class="option-content">
                <div class="option-title">${branch}</div>
                <div class="option-description">从此分支创建新标签</div>
            </div>
        </div>
    `).join('');
}

// 选择选项（标签或分支）
function selectOption(element, type, value) {
    // 移除同类型其他选项的选中状态
    const container = element.parentElement;
    container.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    element.classList.add('selected');
    element.querySelector('input[type="radio"]').checked = true;

    // 如果是分支选择，显示新标签输入框
    if (type === 'branch') {
        document.getElementById('new-tag-group').style.display = 'block';
    }

    // 检查表单完整性
    checkFormValidity();
}

// 切换工作流
function switchWorkflow(workflow) {
    // 更新工作流标签页状态
    document.querySelectorAll('.workflow-tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.workflow-content').forEach(c => c.classList.remove('active'));

    event.target.classList.add('active');
    document.getElementById(`${workflow}-workflow`).classList.add('active');

    currentWorkflow = workflow;

    // 清除之前的选择
    document.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });

    if (workflow === 'existing') {
        // 清除分支和新标签选择
        document.querySelectorAll('input[name="branch_name"]').forEach(input => input.checked = false);
        document.getElementById('new_tag_name').value = '';
        document.getElementById('new-tag-group').style.display = 'none';
    } else {
        // 清除标签选择
        document.querySelectorAll('input[name="selected_tag"]').forEach(input => input.checked = false);
    }

    checkFormValidity();
}

// 检查表单完整性
function checkFormValidity() {
    const taskName = document.getElementById('task_name').value.trim();
    const repository = selectedRepository;
    let isValid = false;

    if (taskName && repository) {
        if (currentWorkflow === 'existing') {
            const selectedTag = document.querySelector('input[name="selected_tag"]:checked');
            isValid = !!selectedTag;
        } else {
            const selectedBranch = document.querySelector('input[name="branch_name"]:checked');
            const newTagName = document.getElementById('new_tag_name').value.trim();
            isValid = !!(selectedBranch && newTagName);
        }
    }

    document.getElementById('submit-btn').disabled = !isValid;
}

// 构建提交数据
function buildSubmitData() {
    const taskName = document.getElementById('task_name').value.trim();
    const repository = selectedRepository;

    const baseData = {
        task_name: taskName,
        repository: {
            id: repository,
            name: repositoryData[repository]?.name || `仓库${repository}`
        },
        workflow_type: currentWorkflow,
        timestamp: new Date().toISOString()
    };

    if (currentWorkflow === 'existing') {
        const selectedTag = document.querySelector('input[name="selected_tag"]:checked');
        return {
            ...baseData,
            action: 'use_existing_tag',
            tag: {
                name: selectedTag.value,
                source: 'existing'
            }
        };
    } else {
        const selectedBranch = document.querySelector('input[name="branch_name"]:checked');
        const newTagName = document.getElementById('new_tag_name').value.trim();
        return {
            ...baseData,
            action: 'create_new_tag',
            branch: {
                name: selectedBranch.value,
                source: 'branch'
            },
            tag: {
                name: newTagName,
                source: 'new',
                base_branch: selectedBranch.value
            }
        };
    }
}

// 表单提交
document.getElementById('training-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // 构建提交数据
    const submitData = buildSubmitData();

    // 显示加载状态
    document.getElementById('submit-btn').style.display = 'none';
    document.getElementById('loading').style.display = 'block';
    document.getElementById('result-message').style.display = 'none';

    // 发送请求
    fetch('/training_task', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData)
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';

        // 显示结果
        const resultMessage = document.getElementById('result-message');
        if (data.success !== false) {
            resultMessage.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 8px;">✅ ${data.message}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">
                    仓库: ${data.repository?.name || data.repository} |
                    ${data.action === 'use_existing_tag' ? `标签: ${data.tag}` : `分支: ${data.branch} → 新标签: ${data.new_tag}`}
                </div>
            `;
            resultMessage.className = 'result-message success';
        } else {
            resultMessage.textContent = data.message || '创建训练任务失败';
            resultMessage.className = 'result-message error';
        }
        resultMessage.style.display = 'block';

        // 成功后重置表单
        if (data.success !== false) {
            setTimeout(() => {
                resetForm();
            }, 3000);
        }
    })
    .catch(error => {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        document.getElementById('submit-btn').style.display = 'inline-block';

        // 显示错误
        const resultMessage = document.getElementById('result-message');
        resultMessage.textContent = '网络错误，请检查连接后重试';
        resultMessage.className = 'result-message error';
        resultMessage.style.display = 'block';

        console.error('Error:', error);
    });
});

// 重置表单
function resetForm() {
    document.getElementById('training-form').reset();
    selectedRepository = null;
    currentWorkflow = 'existing';
    repositoryData = {};

    // 重置UI状态
    document.querySelectorAll('.selection-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelectorAll('.option-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 隐藏工作流区域
    document.getElementById('workflow-section').style.display = 'none';
    document.getElementById('new-tag-group').style.display = 'none';

    // 重置工作流标签页
    document.querySelectorAll('.workflow-tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.workflow-content').forEach(c => c.classList.remove('active'));
    document.querySelector('.workflow-tab').classList.add('active');
    document.getElementById('existing-workflow').classList.add('active');

    // 禁用提交按钮
    document.getElementById('submit-btn').disabled = true;

    // 隐藏结果消息
    document.getElementById('result-message').style.display = 'none';
}

// 监听任务名称输入
document.getElementById('task_name').addEventListener('input', checkFormValidity);
document.getElementById('new_tag_name').addEventListener('input', checkFormValidity);
</script>
{% endblock %}
