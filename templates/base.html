<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}打标平台{% endblock %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #1a73e8; /* Google Blue */
            --primary-dark-color: #1557b0;
            --secondary-color: #34a853; /* Google Green */
            --background-color: #f8f9fa;
            --card-background: #ffffff;
            --text-color: #202124;
            --text-secondary: #5f6368;
            --border-color: #dadce0;
            --shadow-light: rgba(60, 64, 67, 0.1);
            --shadow-medium: rgba(60, 64, 67, 0.15);
            --error-color: #d93025;
            --success-color: #1e8e3e;
        }

        body {
            min-height: 100vh;
            font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            line-height: 1.5;
        }

        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
            padding: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            padding: 0 20px;
            height: 60px;
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
            margin-right: 40px;
            letter-spacing: 0.5px;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            align-items: center;
        }

        .nav-item {
            margin: 0 8px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: white;
            border-radius: 1px;
        }

        /* 主内容区域 */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar-container {
                padding: 0 16px;
            }

            .navbar-brand {
                font-size: 1.3rem;
                margin-right: 20px;
            }

            .main-content {
                padding: 20px 16px;
            }
        }

        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="/" class="navbar-brand">打标平台</a>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="/" class="nav-link {% block nav_home_active %}{% endblock %}">首页</a>
                </li>
                <li class="nav-item">
                    <a href="/steps" class="nav-link {% block nav_steps_active %}{% endblock %}">步骤选择</a>
                </li>
                <li class="nav-item">
                    <a href="/training_task" class="nav-link {% block nav_training_active %}{% endblock %}">训练任务</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        {% block content %}{% endblock %}
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
