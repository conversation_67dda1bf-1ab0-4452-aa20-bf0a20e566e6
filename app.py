# -*- coding: utf-8 -*-
from reprb import reprb
from flask import Flask, render_template, request, jsonify, redirect, url_for
import os
import tempfile
import json
from consts import ALL_RISK, ALL_DBFLAGS, ALL_beginchar

app = Flask(__name__, template_folder="templates")


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/upload_file", methods=["POST"])
def upload_file():
    file = request.files["file"]
    purpose = request.form.get("purpose")

    if not purpose:
        return jsonify({"message": "未指定标记目的"}), 400

    if file:
        with tempfile.NamedTemporaryFile(
            delete=False, mode="wb", suffix=".reprb"
        ) as tmpfile:
            file.save(tmpfile)
            tmpfile_path = tmpfile.name

        return jsonify({"tmpfile_path": tmpfile_path})
    else:
        return jsonify({"message": "No file uploaded"}), 400


@app.route("/upload_files", methods=["POST"])
def upload_files():
    files = request.files.getlist("files")
    purpose = request.form.get("purpose")

    if not purpose:
        return jsonify({"message": "未指定标记目的"}), 400

    if not files:
        return jsonify({"message": "No files uploaded"}), 400

    # 创建一个临时文件来合并所有上传的文件
    with tempfile.NamedTemporaryFile(
        delete=False, mode="wb", suffix=".reprb"
    ) as tmpfile:
        for file in files:
            # 读取每个文件的内容并写入临时文件
            file_content = file.read()
            tmpfile.write(file_content)
            # 如果文件内容不以换行符结尾，添加一个换行符
            if file_content and not file_content.endswith(b'\n'):
                tmpfile.write(b'\n')

        tmpfile_path = tmpfile.name

    return jsonify({"tmpfile_path": tmpfile_path})


@app.route("/process_text", methods=["POST"])
def process_text():
    text_content = request.form.get("text")
    text_format = request.form.get("format", "utf8")  # 默认为 utf8 格式

    if text_content:
        try:
            content_bytes = text_content.encode("utf-8")
            # 如果是 UTF-8 格式，需要转换为 REPRB 格式
            if text_format == "utf8":
                content_bytes = reprb(content_bytes)

            # 使用二进制模式写入
            with tempfile.NamedTemporaryFile(
                delete=False, mode="wb", suffix=".reprb"
            ) as tmpfile:
                tmpfile.write(content_bytes)
                tmpfile_path = tmpfile.name

            return jsonify({"tmpfile_path": tmpfile_path})
        except Exception as e:
            return jsonify({"message": f"处理文本失败: {str(e)}"}), 500
    else:
        return jsonify({"message": "No text provided"}), 400


@app.route("/label_page")
def label_page():
    tmpfile_path = request.args.get("tmpfile_path")
    purpose = request.args.get("purpose")

    if not tmpfile_path or not os.path.exists(tmpfile_path):
        return "临时文件路径丢失或文件不存在！", 400

    if not purpose:
        return "未指定标记目的！", 400

    from reprb import evalb

    processed_lines = []
    total_chars = 0
    # 从临时的reprb文件中读取reprb_bytes形式的payload
    with open(tmpfile_path, "rb") as f:
        for i, reprb_payload in enumerate(f):
            try:
                display_content = evalb(reprb_payload).decode("utf-8")
            except UnicodeDecodeError:
                # 解码失败，使用repr展示bytes
                display_content = reprb_payload.decode()

            total_chars += len(display_content)
            processed_lines.append(
                {
                    "line_number": i + 1,
                    "display_content": display_content,
                    "payload": reprb_payload.decode(),
                }
            )

    risk_tags = list(ALL_RISK.keys())
    db_tags = list(ALL_DBFLAGS.keys())
    beginchar_tags = list(ALL_beginchar.keys())

    return render_template(
        "label.html",
        processed_lines=processed_lines,
        tmpfile_path=tmpfile_path,
        purpose=purpose,
        total_chars=total_chars,
        total_lines=len(processed_lines),
        risk_tags=risk_tags,
        db_tags=db_tags,
        beginchar_tags=beginchar_tags,
    )


@app.route("/submit_labels", methods=["POST"])
def submit_labels():
    from flask import send_file

    purpose = request.form.get("purpose")
    export_filename = request.form.get("export_filename")
    if not purpose:
        return jsonify({"message": "未指定标记目的！"}), 400
    if not export_filename:
        return jsonify({"message": "未指定导出文件名！"}), 400

    def asm_fp(item):
        return {
            "payload": item["payload"],
            "benign": True,
        }

    def asm_mal(item):
        return {"payload": item["payload"], "benign": False, "tags": item["tags"]}

    try:
        data = json.loads(request.form.get("data"))

        if purpose == "false_positive":
            asm_func = asm_fp
        else:
            asm_func = asm_mal

        dump_data = list(map(asm_func, data))

        with tempfile.NamedTemporaryFile(
            delete=False, mode="w", suffix=".json"
        ) as tmpfile:
            json.dump(dump_data, tmpfile)
            tmpfile.flush()  # 确保写入磁盘
            tmpfile_path = tmpfile.name

            response = send_file(
                tmpfile_path,
                as_attachment=True,
                download_name=export_filename,
                mimetype="application/json",
            )

            # @response.call_on_close
            # def cleanup():
            #     os.remove(tmpfile.name)

            return response

    except Exception as e:
        return jsonify({"message": f"保存标记时发生错误：{str(e)}"}), 500


from flask import session

# 步骤选择路由
@app.route('/steps', methods=['GET', 'POST'])
def steps():
    if request.method == 'POST':
        # 获取用户选择的步骤
        step1 = request.form.get('step1')
        step2 = request.form.get('step2')

        # 根据选择跳转到不同界面
        if step1 == 'option1' and step2 == 'optionA':
            return redirect(url_for('label_page'))
        elif step1 == 'option1' and step2 == 'optionB':
            return redirect(url_for('tags'))
        elif step1 == 'option2' and step2 == 'optionA':
            return redirect(url_for('index'))
        else:
            return redirect(url_for('steps'))

    # 首次访问显示选择表单
    return render_template('steps.html')


# 训练任务创建路由
@app.route('/training_task', methods=['GET', 'POST'])
def training_task():
    if request.method == 'POST':
        # 获取JSON数据
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400

        # 提取基本信息
        task_name = data.get('task_name')
        repository_info = data.get('repository', {})
        workflow_type = data.get('workflow_type')
        action = data.get('action')

        if not all([task_name, repository_info.get('id'), workflow_type, action]):
            return jsonify({"success": False, "message": "缺少必要的参数"}), 400

        # 根据不同的操作类型处理
        if action == 'use_existing_tag':
            tag_info = data.get('tag', {})
            # TODO: 在这里实现使用已有标签创建训练任务的逻辑
            # 例如：
            # - 验证标签是否存在
            # - 检查权限
            # - 创建训练任务记录
            # - 启动训练流程

            return jsonify({
                "success": True,
                "message": f"训练任务 '{task_name}' 创建成功",
                "task_id": f"task_{task_name}_{repository_info['id']}_{tag_info['name']}",
                "repository": repository_info['name'],
                "tag": tag_info['name'],
                "action": "use_existing_tag",
                "workflow_type": workflow_type,
                "timestamp": data.get('timestamp')
            })

        elif action == 'create_new_tag':
            branch_info = data.get('branch', {})
            tag_info = data.get('tag', {})

            # TODO: 在这里实现创建新标签并创建训练任务的逻辑
            # 例如：
            # - 验证分支是否存在
            # - 检查标签名是否已存在
            # - 创建新标签
            # - 创建训练任务记录
            # - 启动训练流程

            return jsonify({
                "success": True,
                "message": f"训练任务 '{task_name}' 创建成功",
                "task_id": f"task_{task_name}_{repository_info['id']}_{tag_info['name']}",
                "repository": repository_info['name'],
                "branch": branch_info['name'],
                "new_tag": tag_info['name'],
                "action": "create_new_tag",
                "workflow_type": workflow_type,
                "timestamp": data.get('timestamp')
            })
        else:
            return jsonify({"success": False, "message": "无效的操作类型"}), 400

    # 模拟仓库数据
    repositories = {
        'A': {
            'name': '仓库A',
            'tags': ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0'],
            'branches': ['main', 'develop', 'feature/new-model', 'hotfix/bug-fix']
        },
        'B': {
            'name': '仓库B',
            'tags': ['v0.9.0', 'v1.0.0', 'v1.1.0', 'v1.3.0'],
            'branches': ['master', 'dev', 'feature/enhancement', 'release/v1.4']
        },
        'C': {
            'name': '仓库C',
            'tags': ['v2.0.0', 'v2.1.0', 'v2.2.0', 'v3.0.0'],
            'branches': ['main', 'staging', 'feature/optimization', 'bugfix/critical']
        }
    }

    return render_template('training_task.html', repositories=repositories)


# API路由：获取仓库的标签和分支
@app.route('/api/repository/<repo_id>')
def get_repository_info(repo_id):
    repositories = {
        'A': {
            'name': '仓库A',
            'tags': ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0'],
            'branches': ['main', 'develop', 'feature/new-model', 'hotfix/bug-fix']
        },
        'B': {
            'name': '仓库B',
            'tags': ['v0.9.0', 'v1.0.0', 'v1.1.0', 'v1.3.0'],
            'branches': ['master', 'dev', 'feature/enhancement', 'release/v1.4']
        },
        'C': {
            'name': '仓库C',
            'tags': ['v2.0.0', 'v2.1.0', 'v2.2.0', 'v3.0.0'],
            'branches': ['main', 'staging', 'feature/optimization', 'bugfix/critical']
        }
    }

    if repo_id in repositories:
        return jsonify(repositories[repo_id])
    else:
        return jsonify({"error": "仓库不存在"}), 404


if __name__ == "__main__":
    app.run(debug=True, port=5000)
